import asyncio
import copy
import itertools
import json
import logging
import os
import time
from typing import Union

from fastapi import HTTPException
from langchain.schema import Document
from langchain_core.vectorstores import VectorStore
from langchain_qdrant import QdrantVectorStore

from ira_chat.db import api as db_api, models
from ira_chat.services import file_formats
from ira_chat.services import langchain_svc
from ira_chat.services import detection
from ira_chat.services import extraction
from ira_chat.services import webhooks
from ira_chat.services import jobs
from ira_chat.services import postgres_docstore
from ira_chat.services import retrievers
from ira_chat.services import dataset_utils
from ira_chat.services import llm as ira_llm
from ira_chat.services import vectorstore as ira_vectorstore
from ira_chat.services.dataset_utils import watch_dataset, watch_index
from ira_chat.utils import utils, json_utils
from ira_chat.utils.utils import build_status

logger = logging.getLogger(__name__)
SMALL_SLEEP = 0.01
LANGCHAIN_TYPE = 'langchain'
LLAMA_TYPE = 'llama'

INDEX_PARENT = 'parent_child'
INDEX_NORMAL = 'normal'

document_prompt = """
Perform the following tasks on the provided image:
1. Provide a detailed description of the image.
2. Extract all text from the image and output it as clean plain text.

Ensure the output does not contain repeated punctuation or extra whitespace.
"""

metadata_prompt = """
Extract comprehensive metadata from the document.
Fields to extract:
- Date: Document date, which can be a signature, seal, or issue date (format: YYYY-MM-DD, e.g., 2023-10-25).
- Type: Category or type of the document.
- Name: Full title or name of the document.
- Author: Name of the document's author, if available.
- Keywords: A list of relevant keywords.
- Summary: A brief summary highlighting the document's key points.

Output must be in valid JSON format enclosed within triple backticks:
```
{
  "date": "YYYY-MM-DD",
  "type": "document type",
  "name": "document name",
  "author": "author name",
  "keywords": ["keyword1", "keyword2"],
  "summary": "brief summary"
}
```
"""


class EngineManager:
    def __init__(self, org: models.Organization, ws: models.Workspace, app=None):
        engine_type = global_engine_type()
        self.type = engine_type
        self.engine_context = None
        self.ws = ws
        self.ws_config = ws.get_config()
        self.org = org
        self.org_config = self.org.get_config()
        self._index_suffix = None
        self.index_type = INDEX_NORMAL

        self.app_type = models.APP_TYPE_CHAT
        self.app_config = {}
        self.app = None
        self.default_llm_type = self.ws_config.get('llm_type') or self.org_config.get('llm_type')
        if app:
            self.app_type = self.get_app_type(app)
            self.app_config = self.process_app_config(app)
            self.app = app
        else:
            self.app_config = self.ws_config.copy()
        # Override llm_type if it is set in app_config
        if self.app_config.get('llm_type'):
            self.org_config['llm_type'] = self.app_config['llm_type']

        self._check_llm_enabled()
        self._vectorstore = None
        self._not_topics_vectorstore = None
        self._topics_vectorstore = None
        self._answers_vectorstore = None
        self._init_llm, self._init_llm_params = None, None
        self._index = None
        self._dataset = None
        self._validated = False

    @classmethod
    async def init_async(cls, org: models.Organization, ws: models.Workspace, app=None):
        instance = cls(org, ws, app)
        await instance._validate_index_llm()
        return instance

    def process_app_config(self, app: models.Chat | models.Detection | models.Extractor):
        app_config = app.get_config()
        ws_config = copy.deepcopy(self.ws_config)

        # Traverse ws_config params to chat_config if they are not set in chat config
        default_llm = self.ws_config.get('llm_type') or self.org_config.get('llm_type')
        if app_config.get('llm_type') and app_config.get('llm_type') != default_llm:
            # Re-assign default models
            to_llm = app_config['llm_type']
            ws_config['model_name'] = utils.llm_model_name_by_llm_type(to_llm)
            ws_config['vision_model'] = utils.llm_vision_model_by_llm_type(to_llm)
            logger.info(f"[Engine] Use LLM {to_llm}")

        # Resolve ws config first via default app config
        ws_app_config = self.ws_config.get('apps_config', {}).get(self.app_type, {})
        # Override part of ws_config with params set for ws config per app
        for param in ws_app_config:
            if ws_app_config[param] is not None:
                ws_config[param] = ws_app_config[param]

        # Set defaults for app from ws_config
        dict_checks = ['prompt_config']
        for param in ws_config:
            if param in dict_checks:
                if param in app_config:
                    app_config[param] = json_utils.dict_merge(ws_config[param], app_config[param])
                else:
                    app_config[param] = ws_config[param]
            else:
                if not app_config.get(param) and param in ws_config:
                    app_config[param] = ws_config[param]

        return app_config

    @staticmethod
    def get_app_type(app: models.Chat | models.Detection | models.Extractor):
        if isinstance(app, models.Chat):
            return models.APP_TYPE_CHAT
        if isinstance(app, models.Detection):
            return models.APP_TYPE_DETECTION
        if isinstance(app, models.Extractor):
            return models.APP_TYPE_EXTRACTOR
        return None

    async def _validate_index_llm(self):
        dataset_name, index_name = self.get_dataset_index_names()
        self._validated = True
        if not dataset_name:
            return

        await self._dataset_index(dataset_name, index_name)
        # if index.embedding_provider != main_llm_type:
        #     raise HTTPException(
        #         400,
        #         f'Index "{index_name}" has different LLM type "{index.llm_type}" than the main LLM type "{main_llm_type}"'
        #     )

    async def _dataset_index(self, ds_name, idx_name):
        if self._dataset:
            return self._dataset, self._index
        dataset = await db_api.get_dataset_by_org_and_name(self.org.id, ds_name)
        index = await db_api.get_dataset_index_by_dataset_and_name(dataset.id, idx_name)
        self._dataset, self._index = dataset, index
        return dataset, index

    def get_dataset_index_names(self):
        return dataset_utils.get_dataset_index_names(self.app_config)

    def _get_named_vectorstore(self, suffix, override_llm_type=None, index_config=None, only_if_exists=False):
        org_config = self.org_config.copy()
        if override_llm_type:
            org_config['llm_type'] = override_llm_type
        else:
            if self._index:
                org_config['llm_type'] = self._index.embedding_provider
        return langchain_svc.copy_prepare_vectorstore(
            ira_vectorstore.get_vectorstore(self.type, org_config, self.org.id, self.ws.id, index_config),
            ira_vectorstore.get_index_namespace(suffix),
            only_if_exists=only_if_exists,
        )

    def _init_named_vectorstore(
        self,
        attr,
        suffix,
        override_llm_type=None,
        index_config=None,
        only_if_exists=False,
    ) -> QdrantVectorStore | VectorStore:
        value = getattr(self, attr)
        if value is None:
            setattr(self, attr, self._get_named_vectorstore(suffix, override_llm_type, index_config, only_if_exists))
            return getattr(self, attr)
        return value

    @property
    def index_suffix(self):
        if self._index_suffix:
            return self._index_suffix

        dataset_name, index_name = self.get_dataset_index_names()
        if not dataset_name:
            raise HTTPException(400, 'Dataset/index name is not set in app config')

        self._index_suffix = index_namespace_suffix(self.org, models.Dataset(name=dataset_name), index_name)
        return self._index_suffix

    @property
    def index_suffix_answers(self):
        index_suffix = self.index_suffix
        return f'{index_suffix}-corrected'

    @property
    def vectorstore(self):
        # Only this and answers vectorstore should override config
        return self._init_named_vectorstore(
            '_vectorstore', self.index_suffix, index_config=self._index.get_config()
        )

    @property
    def vectorstore_answers(self):
        # Only this and answers vectorstore should override config
        return self._init_named_vectorstore(
            '_answers_vectorstore', self.index_suffix_answers, index_config=self._index.get_config(),
            only_if_exists=True
        )

    @property
    def not_topics_vectorstore(self):
        suffix = index_namespace_suffix(self.org, self.ws, None)
        return self._init_named_vectorstore(
            '_not_topics_vectorstore', f'{suffix}-not-topics',
            override_llm_type=self.default_llm_type
        )

    @property
    def topics_vectorstore(self):
        suffix = index_namespace_suffix(self.org, self.ws, None)
        return self._init_named_vectorstore(
            '_topics_vectorstore', f'{suffix}-topics',
            override_llm_type=self.default_llm_type
        )

    def get_default_prompts(self) -> dict:
        return langchain_svc.get_default_prompts(self.app_config)

    async def _get_retriever(self, search_k=None, vectorstore=None):
        if not self._validated:
            await self._validate_index_llm()
        search_type = 'similarity_score_threshold'  # similarity, similarity_score_threshold, mmr
        search_kwargs = dict(
            score_threshold=float(self.app_config.get('search_score_threshold') or 0.6),
            k=search_k or self.app_config.get('search_k', 16),
        )
        index_type = self.index_type
        if self._index:
            index_type = self._index.config.get('index_type', index_type)

        if not vectorstore:
            vectorstore = self.vectorstore
        if index_type == INDEX_PARENT:
            doc_store = postgres_docstore.PostgresDocStore() if not self._index else postgres_docstore.PostgresDatasetsDocStore()
            # Double top-k it search_k is not provided
            search_kwargs['k'] *= 2 if not search_k else 1
            search_type = 'similarity'
            retriever = retrievers.ParentDocumentRetrieverWithScore(
                vectorstore=vectorstore,
                docstore=doc_store,
                child_splitter=file_formats.get_child_splitter(),
                # parent_splitter=parent_splitter,
                search_kwargs=search_kwargs,
                search_type=search_type,
                id_key='parent_id',
            )

        else:
            retriever = retrievers.VectorStoreRetrieverWithScore(
                vectorstore=vectorstore,
                search_kwargs=search_kwargs,
                search_type=search_type
            )
            # retriever = self.vectorstore.as_retriever(search_kwargs=search_kwargs, search_type=search_type)
        return retriever

    async def search_in_docs(self, query: str, limit=10) -> list[Document]:
        retriever = await self._get_retriever(search_k=limit)
        results = await retriever.ainvoke(query)
        return results

    def _check_llm_enabled(self):
        # LLM enabled check
        llm_type = self.org_config['llm_type']
        llm_config = self.org_config.get(llm_type)
        if not llm_config:
            return
        enabled = llm_config.get('enabled')
        if enabled is None:
            return
        if not enabled:
            raise HTTPException(400, f'Organization has disabled using service "{llm_type}"')

    def app_llm_type(self):
        return self.org_config['llm_type']

    def get_embedding_model(self):
        return ira_vectorstore.get_embedding_model(self.org_config, self.org.id, self.ws.id)

    def _override_config(self, new_config: dict = None):
        config = copy.deepcopy(self.app_config)
        if new_config:
            for k in new_config:
                config[k] = new_config[k]

        return config

    @staticmethod
    async def create_metric(org_id, workspace_id, app_id, type_, obj_id=None, app_type=models.APP_TYPE_CHAT, value=1):
        metric = {
            'org_id': org_id,
            'workspace_id': workspace_id,
            'app_id': app_id,
            'app_type': app_type,
            'object_id': obj_id,
            'type': type_,
            'value': value,
        }
        await db_api.create_metric(metric)

    def get_init_llm(self, config=None):
        if self._init_llm is not None:
            return self._init_llm, self._init_llm_params

        config = self._override_config(config)
        params = {
            'request_timeout': 300,
            'temperature': 0.2,
            'model_name': utils.llm_model_name_by_llm_type(self.org_config.get('llm_type', 'openai')),
            'vision_model': None,
            'mode': 'condense_question',
            'language': 'english',
            'max_tokens': None,
            'need_vision': None,
            'llm_type': None,
            'index_type': None,
        }
        if config:
            for k in config:
                params[k] = config[k]
        init_llm = ira_llm.init_llm_class(self.org_config, params, self.org.id, self.ws.id)
        self._init_llm, self._init_llm_params = init_llm, params
        return init_llm, params

    def get_llm(self, config=None):
        init_llm, params = self.get_init_llm(config=config)
        return init_llm.init_llm(params)

    def get_embeddings(self, config=None):
        init_llm, params = self.get_init_llm(config=config)
        return init_llm.init_embeddings()

    def index_new_docs(self, docs: list[Document], ws: models.Workspace, force_index_recreate=False) -> list[str]:
        if force_index_recreate:
            langchain_svc.prepare_vectorstore(
                self.vectorstore,
                ira_vectorstore.get_index_namespace(self.index_suffix),
                index_recreate=force_index_recreate,
            )

        if self.index_type == INDEX_PARENT:
            # set ids
            for doc in docs:
                id = utils.generate_unicode_uuid()
                doc.metadata['id'] = id
            # child split
            child_docs = file_formats.split_docs(docs, splitter=file_formats.get_child_splitter())
            # add parent_id to child docs
            for child_list in child_docs:
                for child in child_list:
                    child.metadata['parent_id'] = child.metadata.pop('id')

            # index child docs
            all_child_docs = list(itertools.chain.from_iterable(child_docs))
            ids = [utils.generate_unicode_uuid() for _ in all_child_docs]
            # Send ids because otherwise ids somehow have different values:
            # auto-generated (and returned) vs. inserted in vectorstore
            ids = self.vectorstore.add_documents(all_child_docs, ids=ids)
            # store parent data
            for doc in docs:
                db_api.create_document_sync({
                    'doc_metadata': json.dumps(doc.metadata),
                    'content': doc.page_content,
                    'file_id': doc.metadata['file_id'],
                    'id': doc.metadata['id'],
                })
            # return child doc ids
            return ids
        else:
            ids = [utils.generate_unicode_uuid() for _ in docs]
            # Send ids because otherwise ids somehow have different values:
            # auto-generated (and returned) vs. inserted in vectorstore
            ids = self.vectorstore.add_documents(docs, ids=ids)
            return ids

    @jobs.job_tracker
    @watch_dataset(update_stats=True)
    @watch_index(update_stats=True)
    async def index_file_for_extraction(
        self,
        file_data: bytes,
        extractor: models.Extractor,
        ext_file: models.DatasetFile,
        mode='llm'
    ):
        # Let the main thread go
        await asyncio.sleep(SMALL_SLEEP)
        docs = None
        llm = None
        try:
            ext_config = extractor.get_config()

            ext_config.update({
                'need_vision': True,
                # 'max_tokens': 2048,
                'max_tokens': ext_config.get('max_tokens') or 16384,
                # 'max_output_tokens': 2048,
                'max_output_tokens': ext_config.get('max_output_tokens') or 16384,
                'presence_penalty': 0.0,
                'frequency_penalty': 0.5,  # For fixing the issue with the repeated symbols
                'top_p': 1.0,
                'app_type': models.APP_TYPE_EXTRACTOR_FILE,
                'app_id': extractor.id,
                'object_id': ext_file.id,
            })
            # model_name and vision model already overridden in the init
            ext_config.pop('model_name', None)
            ext_config.pop('vision_model', None)

            status = build_status(models.STATUS_PROCESSING)
            await db_api.update_extractor(extractor, {'status': status})
            llm = self.get_llm(ext_config)
            logger.info(f'Processing {ext_file.name}')
            docs = await detection.extract_document_data(
                ext_file.name,
                file_data,
                self.app_llm_type(),
                llm,
                document_prompt,
            )
            splitted_docs = file_formats.split_docs_flat(docs)
            ids = [utils.generate_unicode_uuid() for _ in splitted_docs]
            ids = self.vectorstore.add_documents(splitted_docs, ids=ids)

            embs = [
                {'dataset_file_id': ext_file.id, 'dataset_file_hash': ext_file.hash, 'id': i, 'index_id': self._index.id}
                for i in ids
            ]
            await db_api.create_dataset_file_embeddings(embs)
            del embs
        except Exception as exc:
            logger.exception(f'Failed processing file {ext_file.name}: {str(exc)}')
            msg = f'{exc.__class__.__name__}: {str(exc)}'
            status = build_status(models.STATUS_ERROR, msg)
        else:
            logger.info(f'Done processing the file {ext_file.name}, status: {models.STATUS_PROCESSING_META}')
            status = build_status(models.STATUS_PROCESSING_META)

        await db_api.update_dataset_file(ext_file, {'status': status})
        await db_api.update_extractor(extractor, {'status': status, 'has_updates': True})
        if docs is not None:
            # Trigger metadata generation
            status = await detection.process_metadata(
                extractor,
                ext_file,
                file_data,
                self.app_llm_type(),
                llm,
                metadata_prompt,
                docs,
            )
            logger.info(f'Done processing metadata of file {ext_file.name}, status: {status}')
        ext_file.status = status
        return ext_file

    async def get_index_contents(self, file_db: models.File | models.DatasetFile) -> list[Document]:
        kwargs = {'file_id': file_db.id}
        embeddings = await db_api.list_dataset_file_embeddings(**kwargs)
        ids = [e.id for e in embeddings]

        docs = ira_vectorstore.get_indexed_contents_by_ids(
            self.vectorstore,
            ids=ids,
            namespace=None
        )
        return docs

    async def delete_embeddings_extractor(self, file_db: models.ExtractorFile):
        embeddings = await db_api.list_dataset_file_embeddings(file_id=file_db.id)

        logger.info(f'Deleting {len(embeddings)} vectorstore embeddings associated with {file_db.name}...')
        self.delete_embeddings(embeddings)

        await db_api.delete_dataset_file_embeddings(file_id=file_db.id)

    async def process_extractor(
        self, ext: models.Extractor, user_id: int, access_type='session', wait_files=False
    ):
        extractor_res: models.ExtractorResult = await db_api.create_extractor_result({
            'owner_id': user_id,
            'workspace_id': self.ws.id,
            'extractor_id': ext.id,
            # 'config': self.app_config.copy(),
            'status': build_status(models.STATUS_PROCESSING),
        })
        all_results = await db_api.list_extractor_results(extractor_id=ext.id, order='created_at', desc=True)
        if len(all_results) > ext.get_config().get('max_results', 10):
            await db_api.delete_extractor_results(extractor_id=ext.id, ids=[r.id for r in all_results[10:]])
        await db_api.update_extractor(ext, {'status': extractor_res.status})

        await self.create_metric(
            self.org.id, self.ws.id, app_id=ext.id, type_=models.METRIC_EXTRACTOR_RESULT,
            app_type=models.APP_TYPE_EXTRACTOR, obj_id=extractor_res.id,
        )
        t = asyncio.create_task(self._process_extractor(ext, extractor_res, access_type, wait_files))
        return extractor_res

    async def _process_extractor(
        self, ext: models.Extractor, ext_res: models.ExtractorResult, access_type='session', wait_files=False
    ):
        await asyncio.sleep(SMALL_SLEEP)

        if wait_files:
            logger.info(f"[EXTRACTOR id={ext.id}] Waiting for files to be processed")
            timeout = 600
            t = time.time()
            while True:
                files = await db_api.list_dataset_files(dataset_id=self._dataset.id)
                all_good = all(f.status['status'] == models.STATUS_SUCCESS for f in files)
                error = any(f.status['status'] == models.STATUS_ERROR for f in files)
                processing = any(f.status['status'] in [models.STATUS_PROCESSING, models.STATUS_PROCESSING_META] for f in files)
                if processing:
                    await asyncio.sleep(2)
                    continue
                if error:
                    logger.error(f"[EXTRACTOR id={ext.id}] Error in files")
                    break
                if all_good:
                    break
                if time.time() - t > timeout:
                    logger.error(f"[EXTRACTOR id={ext.id}] Timeout waiting for files")
                    break

        logger.info(f"[EXTRACTOR id={ext.id}] Start processing extractor")

        config = ext.get_config()
        config.update({
            # 'need_vision': True,
            'max_tokens': 16384,
            'max_output_tokens': 16384,
            'app_type': models.APP_TYPE_EXTRACTOR,
            'app_id': ext_res.extractor_id,
            'object_id': ext_res.id,
        })
        # model_name and vision model already overridden in the init
        config.pop('model_name', None)
        config.pop('vision_model', None)

        try:
            llm = self.get_llm(config=config)
        except Exception as e:
            msg = f'{e.__class__.__name__}: {str(e)}'
            updated = {'status': build_status(models.STATUS_ERROR, msg)}

            await db_api.update_extractor_result(ext_res, updated)
            logger.exception(msg)
            return

        await extraction.process_extractor(llm, self.vectorstore, ext, ext_res, access_type=access_type)

    @jobs.job_tracker
    async def process_extractor_request(self, ext: models.ExtractorItem, out: models.ExtractorItemOutput, access_type='session'):
        await asyncio.sleep(SMALL_SLEEP)

        config = self.app_config.copy()
        config.update({
            # 'need_vision': True,
            'max_tokens': 16384,
            'max_output_tokens': 16384,
            'app_type': models.APP_TYPE_EXTRACTOR_REQ,
            'app_id': out.extractor_id,
            'object_id': out.id,
        })
        # model_name and vision model already overridden in the init
        config.pop('model_name', None)
        config.pop('vision_model', None)

        try:
            llm = self.get_llm(config=config)
        except Exception as e:
            msg = f'{e.__class__.__name__}: {str(e)}'
            updated = {
                'output': msg,
                'status': models.STATUS_ERROR,
            }

            await db_api.update_extractor_item_output(out, updated)
            await db_api.update_extractor_item(ext, {'status': updated['status']})
            logger.exception(msg)
            return

        await extraction.process_extractor_request(llm, self.vectorstore, self.app, ext, out, access_type=access_type)

    def delete_embeddings(self, embeddings: list[models.DatasetFileEmbeddings]):
        ids = [e.id for e in embeddings]
        if ids:
            self.vectorstore.delete(ids)

    @jobs.job_tracker
    async def process_detection(
        self,
        det: models.DetectionItem,
        out: models.DetectionItemOutput,
        access_type='session',
    ):
        config = self.app_config.copy()
        config.update({
            'need_vision': True,
            'max_tokens': 16384,
            'max_output_tokens': 16384,
            'app_type': models.APP_TYPE_DETECTION,
            'app_id': det.detection_id,
            'object_id': out.id,
        })
        # model_name and vision model already overridden in the init
        config.pop('model_name', None)
        config.pop('vision_model', None)
        llm = None

        try:
            if det.type in ['prescription', 'prompt']:
                llm = self.get_llm(config=config)
        except Exception as e:
            # Only update in case of error
            msg = f'{e.__class__.__name__}: {str(e)}'
            updated_det = {
                'output': msg,
                'status': models.STATUS_ERROR,
            }

            await db_api.update_detection_item(det, {'status': updated_det['status']})
            await db_api.update_detection_item_output(out, updated_det)
            logger.exception(msg)
            return

        await detection.process_detection(
            self.org, self.ws, llm, self.app, det, out, llm_type=self.app_llm_type(), access_type=access_type
        )

    async def send_all_webhooks(
        self,
        data: dict,
        sender: Union[models.Workspace, models.Organization]
    ):
        org_webhooks = await db_api.list_org_webhooks(self.org.id)
        for org_webhook in org_webhooks:
            await webhooks.send_webhook(org_webhook, sender, data)


def index_namespace_suffix(org: models.Organization, ws: models.Workspace | models.Dataset, suffix: str | None):
    if not suffix:
        return f'{org.name}-{ws.name}'
    else:
        return f'{org.name}-{ws.name}-{suffix}'


def global_engine_type():
    engine_type = os.getenv('ENGINE_TYPE', LANGCHAIN_TYPE)
    return engine_type
